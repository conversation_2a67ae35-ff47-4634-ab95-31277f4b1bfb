# 📊 政府报告图表增强

**文档：** 01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md  
**任务：** 为文档添加专业图表，提升视觉效果和专业度

## 执行步骤
1. 分析文档结构和内容
2. 识别适合添加图表的位置（数据段落、对比分析、趋势说明等）
3. 设计多样化图表类型（柱状图、饼图、流程图、地图等）
4. 生成图表代码并保存到指定目录
5. 生成带图表引用的新markdown文件

## 图表规范
- **保存路径：** `output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告/charts/`
- **命名规则：** `chart_章节编号_图表类型_序号.py` 和 `chart_章节编号_图表类型_序号.png`
- **引用格式：** `![图表标题](./charts/chart_章节编号_图表类型_序号.png)`
- **图表尺寸：** 统一使用 `plt.figure(figsize=(12, 8))` 或根据内容调整

## 质量标准
- ✅ 图表分布均匀合理（每2-3个段落考虑一个图表位置）
- ✅ 设计专业美观（政府报告标准，配色协调）
- ✅ 类型丰富多样（至少包含5种不同类型图表）
- ✅ 代码规范完整（包含数据生成、样式设置、保存功能）
- ✅ 文件管理有序（统一目录结构，清晰命名）

## 参考资源
- **图表代码参考：** `seaborn_chart_示例代码.py`
- **重点参考：** `seaborn_chart_示例代码.py` 的中文正确显示方法
- **输出文件：** `output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告/markdown/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告_enhanced.md`

## 图表类型建议
- 📊 数据对比：柱状图、条形图
- 🥧 比例展示：饼图、环形图
- 📈 趋势分析：折线图、面积图
- 🗺️ 地理信息：地图、热力图
- 📋 流程展示：流程图、组织架构图
- 📊 多维数据：散点图、雷达图

**开始执行：** 请先提供目标文件内容，我将按照上述规范进行图表增强处理。



---

# 📷 政府报告真实图片增强

**文档：** 01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md  
**任务：** 为文档搜索并添加高质量真实图片，提升文档专业度

## 执行步骤
1. 分析文档内容，识别需要真实图片的位置
2. 生成精准的图片搜索关键词
3. 多源搜索高质量真实图片
4. 筛选并下载符合要求的图片
5. 生成带真实图片的新markdown文件

## 图片规范
- **保存路径：** `output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告/real_photos/`
- **命名规则：** `real_章节编号_图片类型_序号.jpg`
- **引用格式：** `![图片标题](./real_photos/real_章节编号_图片类型_序号.jpg)`
- **质量要求：** 分辨率≥1920x1080，文件大小>100KB

## 质量标准
- ✅ 图片内容与文档高度匹配（政府、农业、通州区相关）
- ✅ 图片质量专业清晰（商业级拍摄水准）
- ✅ 版权合规安全（优先免费商用图片）
- ✅ 地域特色明显（体现北京通州宋庄特色）
- ✅ 分布位置合理（每2-3段落考虑一张图片）

## 图片类型重点
- 🏛️ 政府场景：政府大楼、会议场景、政策宣传
- 🌾 农业现状：农田景观、农产品、农业设施
- 🏘️ 地理环境：通州区风貌、宋庄镇特色建筑
- 💼 商务场景：数字化设备、现代农业技术
- 👥 人文活动：农民工作、游客体验、文化活动

## 参考资源
- **搜索工具：** Web_Access_search（多关键词组合搜索）
- **输出文件：** `output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告_real_photos.md`

**开始执行：** 请先提供目标文件内容，我将进行真实图片搜索和文档增强。


---

# 🤖 政府报告AI图片生成增强

**文档：** 01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告.md  
**任务：** 为文档生成专业概念图片，增强抽象内容的视觉表达

## 执行步骤
1. 分析文档内容，识别需要概念图示的位置
2. 设计详细的AI图片生成prompt
3. 使用jimeng mcp批量生成图片
4. 质量评估和优化重生成
5. 生成带AI图片的新markdown文件

## 图片规范
- **保存路径：** `output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告/ai_generated/`
- **命名规则：** `ai_章节编号_概念类型_序号.png`
- **引用格式：** `![概念图标题](./ai_generated/ai_章节编号_概念类型_序号.png)`
- **生成要求：** 高清晰度，专业商务风格，中文友好

## 质量标准
- ✅ 概念表达准确清晰（抽象概念具象化）
- ✅ 视觉风格统一专业（政府报告标准）
- ✅ 色彩搭配协调美观（蓝绿主题色系）
- ✅ 文字元素清晰可读（支持中文显示）
- ✅ 创意设计新颖独特（避免模板化）

## AI图片类型重点
- 💡 概念图示：农旅融合模式、数字化转型概念
- 🔮 未来愿景：发展蓝图、智慧农业场景
- 📊 流程架构：实施路径、组织架构、技术架构
- 🎯 战略规划：发展目标、市场定位
- 🌐 生态系统：产业链条、合作网络

## 生成参数优化
- **风格定位：** 现代商务、科技感、专业正式
- **色彩方案：** 政府蓝(#1f77b4) + 农业绿(#2E8B57) + 辅助色
- **构图要求：** 简洁大气、重点突出、层次分明
- **质量控制：** 每张图片评分>7分，否则重新生成

## 参考资源
- **生成工具：** jimeng mcp（主要）+ 备用AI图片工具
- **输出文件：** `output/01_北京通州区宋庄镇农旅数字化推广项目可行性研究报告_ai_images.md`
**开始执行：** 请先提供目标文件内容，我将进行AI图片生成和文档增强。
