#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
北京通州区宋庄镇农旅数字化推广项目可行性研究报告 - 图表生成脚本
生成专业的政府报告级别图表，支持中文显示
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 创建输出目录
output_dir = "."
os.makedirs(output_dir, exist_ok=True)

# 中文字体配置（支持多种字体回退）
import platform
system = platform.system()

if system == "Darwin":  # macOS
    plt.rcParams['font.sans-serif'] = [
        'PingFang SC',          # 苹方
        'Hiragino Sans GB',     # 冬青黑体
        'STHeiti',              # 华文黑体
        'Arial Unicode MS',     # Arial Unicode
        'SimHei'                # 黑体
    ]
elif system == "Windows":  # Windows
    plt.rcParams['font.sans-serif'] = [
        'Microsoft YaHei',      # 微软雅黑
        'SimHei',               # 黑体
        'KaiTi',                # 楷体
        'FangSong',             # 仿宋
        'Arial Unicode MS'      # Arial Unicode
    ]
else:  # Linux
    plt.rcParams['font.sans-serif'] = [
        'Noto Sans CJK SC',     # 思源黑体
        'WenQuanYi Micro Hei',  # 文泉驿微米黑
        'SimHei',               # 黑体
        'DejaVu Sans'           # 默认字体
    ]

plt.rcParams['axes.unicode_minus'] = False

# 2025年政府商务配色方案
colors_government = ['#1f77b4', '#2ca02c', '#ff7f0e', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
colors_agriculture = ['#2E8B57', '#32CD32', '#228B22', '#90EE90', '#98FB98', '#F0FFF0', '#006400', '#9ACD32']
colors_blue_series = ['#0066CC', '#3399FF', '#66B2FF', '#99CCFF', '#CCE5FF']
colors_green_series = ['#006600', '#339933', '#66CC66', '#99FF99', '#CCFFCC']

# 专业图表样式配置
def setup_chart_style():
    """设置专业的图表样式"""
    sns.set_style("whitegrid", {
        "axes.spines.left": True,
        "axes.spines.bottom": True,
        "axes.spines.top": False,
        "axes.spines.right": False,
        "grid.color": "#E5E5E5",
        "grid.linewidth": 0.5
    })
    plt.rcParams.update({
        'figure.facecolor': 'white',
        'axes.facecolor': 'white',
        'savefig.facecolor': 'white',
        'savefig.edgecolor': 'none',
        'font.size': 10,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10
    })

def save_chart(filename, dpi=300):
    """统一的图表保存函数"""
    plt.tight_layout()
    plt.savefig(f"{output_dir}/{filename}", dpi=dpi, bbox_inches="tight", 
                facecolor='white', edgecolor='none')
    plt.close()
    print(f"✅ 生成图表: {filename}")

# 图表1：年度游客数量变化趋势柱状图
def generate_chart_01_visitor_trend():
    """生成年度游客数量变化趋势柱状图"""
    setup_chart_style()
    
    # 模拟数据
    years = ['2020', '2021', '2022', '2023', '2024', '2025预测']
    visitors = [15, 12, 18, 25, 32, 50]  # 单位：万人次
    
    plt.figure(figsize=(14, 8))  # 16:9 比例
    bars = plt.bar(years, visitors, color=colors_blue_series[:len(years)],
                   alpha=0.8, edgecolor='white', linewidth=2)
    
    # 添加数值标签
    for bar, value in zip(bars, visitors):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{value}万', ha='center', va='bottom', fontsize=11, fontweight='bold')
    
    plt.title('宋庄镇年度游客数量变化趋势', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('年份', fontsize=12)
    plt.ylabel('游客数量（万人次）', fontsize=12)
    plt.ylim(0, max(visitors) * 1.2)
    
    # 添加趋势线
    x_pos = range(len(years))
    z = np.polyfit(x_pos, visitors, 1)
    p = np.poly1d(z)
    plt.plot(x_pos, p(x_pos), "r--", alpha=0.8, linewidth=2, label='趋势线')
    plt.legend()
    
    save_chart("chart_01_年度游客数量_柱状图.png")

# 图表2：游客来源地分布饼图
def generate_chart_02_visitor_source():
    """生成游客来源地分布饼图"""
    setup_chart_style()
    
    # 模拟数据
    sources = ['北京城区', '通州区', '河北省', '天津市', '其他地区']
    percentages = [45, 25, 15, 10, 5]
    
    plt.figure(figsize=(10, 8))  # 4:3 比例
    colors = colors_government[:len(sources)]
    
    wedges, texts, autotexts = plt.pie(percentages, labels=sources, colors=colors,
                                      autopct='%1.1f%%', startangle=90,
                                      explode=(0.05, 0, 0, 0, 0))
    
    # 美化文字
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontsize(11)
        autotext.set_fontweight('bold')
    
    for text in texts:
        text.set_fontsize(12)
    
    plt.title('宋庄镇游客来源地分布', fontsize=16, fontweight='bold', pad=20)
    plt.axis('equal')
    
    save_chart("chart_02_游客来源地分布_饼图.png")

# 图表3：居民职业分布饼图
def generate_chart_03_resident_occupation():
    """生成宋庄镇居民职业分布饼图"""
    setup_chart_style()
    
    # 模拟数据
    occupations = ['农业生产者', '工商服务业', '艺术工作者', '外出务工', '其他职业']
    percentages = [30, 25, 15, 20, 10]
    
    plt.figure(figsize=(10, 8))  # 4:3 比例
    colors = colors_agriculture[:len(occupations)]
    
    wedges, texts, autotexts = plt.pie(percentages, labels=occupations, colors=colors,
                                      autopct='%1.1f%%', startangle=45,
                                      explode=(0.05, 0, 0.05, 0, 0))
    
    # 美化文字
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontsize(11)
        autotext.set_fontweight('bold')
    
    for text in texts:
        text.set_fontsize(12)
    
    plt.title('宋庄镇居民职业分布', fontsize=16, fontweight='bold', pad=20)
    plt.axis('equal')
    
    save_chart("chart_03_居民职业分布_饼图.png")

# 图表4：满意度与消费金额关系散点图
def generate_chart_04_satisfaction_consumption():
    """生成游客满意度与消费金额关系散点图"""
    setup_chart_style()
    
    # 模拟数据
    np.random.seed(42)
    n_points = 100
    satisfaction = np.random.normal(7.5, 1.5, n_points)
    satisfaction = np.clip(satisfaction, 1, 10)
    
    # 消费金额与满意度正相关，但有一定随机性
    consumption = satisfaction * 50 + np.random.normal(0, 50, n_points)
    consumption = np.clip(consumption, 50, 800)
    
    plt.figure(figsize=(14, 8))  # 16:9 比例
    scatter = plt.scatter(satisfaction, consumption, c=consumption,
                         cmap='viridis', alpha=0.7, s=60, edgecolors='white', linewidth=1)
    
    # 添加趋势线
    z = np.polyfit(satisfaction, consumption, 1)
    p = np.poly1d(z)
    plt.plot(satisfaction, p(satisfaction), "r-", alpha=0.8, linewidth=2, label='趋势线')
    
    plt.colorbar(scatter, label='消费金额（元）')
    plt.xlabel('游客满意度评分', fontsize=12)
    plt.ylabel('消费金额（元）', fontsize=12)
    plt.title('游客满意度与消费金额关系分析', fontsize=16, fontweight='bold', pad=20)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    save_chart("chart_04_满意度与消费关系_散点图.png")

# 图表5：数字素养水平对比条形图
def generate_chart_05_digital_literacy():
    """生成不同群体数字素养水平对比条形图"""
    setup_chart_style()

    # 模拟数据
    groups = ['艺术工作者', '服务业从业者', '管理人员', '农业生产者']
    basic_skills = [85, 70, 65, 45]
    advanced_skills = [60, 40, 55, 15]

    x = np.arange(len(groups))
    width = 0.35

    plt.figure(figsize=(14, 8))  # 16:9 比例
    bars1 = plt.bar(x - width/2, basic_skills, width, label='基础数字技能',
                    color=colors_blue_series[0], alpha=0.8)
    bars2 = plt.bar(x + width/2, advanced_skills, width, label='高级数字技能',
                    color=colors_blue_series[2], alpha=0.8)

    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height}%', ha='center', va='bottom', fontsize=10)

    plt.xlabel('群体类型', fontsize=12)
    plt.ylabel('技能掌握比例（%）', fontsize=12)
    plt.title('宋庄镇不同群体数字素养水平对比', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(x, groups)
    plt.legend()
    plt.ylim(0, 100)
    plt.grid(True, alpha=0.3, axis='y')

    save_chart("chart_05_数字素养水平_条形图.png")

# 图表6：政策环境匹配度雷达图
def generate_chart_06_policy_environment():
    """生成政策环境匹配度评估雷达图"""
    setup_chart_style()

    # 模拟数据
    categories = ['乡村振兴\n战略支持', '数字乡村\n建设政策', '文旅融合\n发展政策',
                  '农村电商\n支持政策', '人才培养\n政策支持', '资金扶持\n政策']
    values = [9, 8, 8.5, 7, 7.5, 6.5]

    # 计算角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    values += values[:1]  # 闭合图形
    angles += angles[:1]

    plt.figure(figsize=(10, 8))  # 4:3 比例
    ax = plt.subplot(111, projection='polar')

    # 绘制雷达图
    ax.plot(angles, values, 'o-', linewidth=2, color=colors_government[0], alpha=0.8)
    ax.fill(angles, values, alpha=0.25, color=colors_government[0])

    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontsize=11)
    ax.set_ylim(0, 10)
    ax.set_yticks([2, 4, 6, 8, 10])
    ax.set_yticklabels(['2', '4', '6', '8', '10'], fontsize=10)
    ax.grid(True)

    plt.title('宋庄镇数字农旅项目政策环境匹配度评估', fontsize=14, fontweight='bold', pad=30)

    save_chart("chart_06_政策环境匹配度_雷达图.png")

# 图表7：投资收益预测对比柱状图
def generate_chart_07_investment_return():
    """生成投资收益预测对比柱状图"""
    setup_chart_style()

    # 模拟数据
    years = ['第1年', '第2年', '第3年', '第4年', '第5年']
    investment = [500, 420, 420, 420, 420]  # 万元
    revenue = [200, 450, 680, 850, 1000]    # 万元

    x = np.arange(len(years))
    width = 0.35

    plt.figure(figsize=(14, 8))  # 16:9 比例
    bars1 = plt.bar(x - width/2, investment, width, label='年度投资',
                    color=colors_government[3], alpha=0.8)
    bars2 = plt.bar(x + width/2, revenue, width, label='年度收益',
                    color=colors_government[1], alpha=0.8)

    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 10,
                    f'{height}万', ha='center', va='bottom', fontsize=10)

    plt.xlabel('年份', fontsize=12)
    plt.ylabel('金额（万元）', fontsize=12)
    plt.title('宋庄镇数字农旅项目投资收益预测', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(x, years)
    plt.legend()
    plt.grid(True, alpha=0.3, axis='y')

    save_chart("chart_07_投资收益预测_柱状图.png")

# 图表8：客户群体需求偏好热力图
def generate_chart_08_customer_preference():
    """生成客户群体需求偏好热力图"""
    setup_chart_style()

    # 模拟数据
    customer_groups = ['城区家庭', '城区青年', '中老年群体', '艺术爱好者', '企业团建']
    activity_types = ['亲子互动', '文化艺术', '自然体验', '农事体验', '休闲放松', '社交互动']

    # 创建偏好矩阵（1-10分）
    preference_matrix = np.array([
        [9, 6, 8, 7, 6, 7],  # 城区家庭
        [5, 9, 7, 6, 6, 8],  # 城区青年
        [4, 5, 8, 6, 9, 7],  # 中老年群体
        [6, 10, 7, 5, 7, 6], # 艺术爱好者
        [7, 6, 6, 5, 7, 9]   # 企业团建
    ])

    plt.figure(figsize=(14, 8))  # 16:9 比例
    sns.heatmap(preference_matrix,
                xticklabels=activity_types,
                yticklabels=customer_groups,
                annot=True,
                cmap='YlOrRd',
                cbar_kws={'label': '偏好程度（1-10分）'},
                fmt='d')

    plt.title('不同客户群体活动偏好热力图', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('活动类型', fontsize=12)
    plt.ylabel('客户群体', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)

    save_chart("chart_08_客户群体偏好_热力图.png")

# 图表9：农业产业结构饼图
def generate_chart_09_agriculture_structure():
    """生成宋庄镇农业产业结构饼图"""
    setup_chart_style()

    # 模拟数据
    crops = ['水稻种植', '蔬菜种植', '果树种植', '花卉种植', '其他作物']
    areas = [35, 30, 20, 10, 5]  # 百分比

    plt.figure(figsize=(10, 8))  # 4:3 比例
    colors = colors_agriculture[:len(crops)]

    wedges, texts, autotexts = plt.pie(areas, labels=crops, colors=colors,
                                      autopct='%1.1f%%', startangle=90,
                                      explode=(0.05, 0, 0, 0, 0))

    # 美化文字
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontsize(11)
        autotext.set_fontweight('bold')

    for text in texts:
        text.set_fontsize(12)

    plt.title('宋庄镇农业产业结构分布', fontsize=16, fontweight='bold', pad=20)
    plt.axis('equal')

    save_chart("chart_09_农业产业结构_饼图.png")

# 图表10：月度客流量变化折线图
def generate_chart_10_monthly_visitors():
    """生成月度客流量变化折线图"""
    setup_chart_style()

    # 模拟数据
    months = ['1月', '2月', '3月', '4月', '5月', '6月',
              '7月', '8月', '9月', '10月', '11月', '12月']
    visitors_2024 = [2.5, 1.8, 3.2, 4.5, 5.8, 4.2,
                     3.8, 4.1, 5.2, 6.8, 4.5, 3.1]  # 万人次
    visitors_2025 = [3.2, 2.5, 4.1, 5.8, 7.2, 5.5,
                     4.8, 5.2, 6.5, 8.5, 5.8, 4.2]  # 万人次预测

    plt.figure(figsize=(14, 8))  # 16:9 比例

    plt.plot(months, visitors_2024, 'o-', linewidth=3, markersize=8,
             color=colors_blue_series[0], label='2024年实际', alpha=0.8)
    plt.plot(months, visitors_2025, 's--', linewidth=3, markersize=8,
             color=colors_blue_series[2], label='2025年预测', alpha=0.8)

    # 添加数值标签
    for i, (v1, v2) in enumerate(zip(visitors_2024, visitors_2025)):
        plt.text(i, v1 + 0.1, f'{v1}万', ha='center', va='bottom', fontsize=9)
        plt.text(i, v2 + 0.1, f'{v2}万', ha='center', va='bottom', fontsize=9)

    plt.title('宋庄镇月度游客流量变化趋势', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('月份', fontsize=12)
    plt.ylabel('游客数量（万人次）', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)

    save_chart("chart_10_月度客流量_折线图.png")

# 图表11：项目建设进度甘特图
def generate_chart_11_project_timeline():
    """生成项目建设进度甘特图"""
    setup_chart_style()

    # 模拟数据
    tasks = ['基础设施建设', '数字平台开发', '农业体验区建设',
             '艺术农园建设', '营销推广', '试运营']
    start_months = [1, 2, 3, 4, 5, 6]
    durations = [4, 3, 3, 2, 2, 1]

    plt.figure(figsize=(14, 8))  # 16:9 比例

    # 创建甘特图
    for i, (task, start, duration) in enumerate(zip(tasks, start_months, durations)):
        plt.barh(i, duration, left=start, height=0.6,
                color=colors_government[i % len(colors_government)], alpha=0.8)
        plt.text(start + duration/2, i, f'{duration}个月',
                ha='center', va='center', fontweight='bold', color='white')

    plt.title('宋庄镇数字农旅项目建设进度计划', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('时间（月）', fontsize=12)
    plt.ylabel('建设任务', fontsize=12)
    plt.yticks(range(len(tasks)), tasks)
    plt.xlim(0, 8)
    plt.grid(True, alpha=0.3, axis='x')

    save_chart("chart_11_项目建设进度_甘特图.png")

# 图表12：风险评估矩阵散点图
def generate_chart_12_risk_assessment():
    """生成项目风险评估矩阵散点图"""
    setup_chart_style()

    # 模拟数据
    risks = ['市场竞争', '技术风险', '资金风险', '政策变化',
             '人才缺失', '天气影响', '疫情影响', '设备故障']
    probability = [7, 4, 3, 2, 6, 5, 3, 4]  # 发生概率 1-10
    impact = [8, 7, 9, 6, 5, 4, 8, 3]       # 影响程度 1-10

    plt.figure(figsize=(12, 9))  # 4:3 比例

    # 创建散点图
    colors_risk = ['red' if p*i > 35 else 'orange' if p*i > 20 else 'green'
                   for p, i in zip(probability, impact)]

    scatter = plt.scatter(probability, impact, s=[p*i*10 for p, i in zip(probability, impact)],
                         c=colors_risk, alpha=0.7, edgecolors='black', linewidth=1)

    # 添加风险标签
    for i, risk in enumerate(risks):
        plt.annotate(risk, (probability[i], impact[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)

    # 添加风险区域划分线
    plt.axhline(y=5, color='gray', linestyle='--', alpha=0.5)
    plt.axvline(x=5, color='gray', linestyle='--', alpha=0.5)

    plt.title('宋庄镇数字农旅项目风险评估矩阵', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('发生概率', fontsize=12)
    plt.ylabel('影响程度', fontsize=12)
    plt.xlim(0, 10)
    plt.ylim(0, 10)
    plt.grid(True, alpha=0.3)

    # 添加图例
    plt.text(0.5, 9.5, '高风险', color='red', fontweight='bold')
    plt.text(0.5, 9, '中风险', color='orange', fontweight='bold')
    plt.text(0.5, 8.5, '低风险', color='green', fontweight='bold')

    save_chart("chart_12_风险评估矩阵_散点图.png")

# 主执行函数
def generate_all_charts():
    """生成所有图表"""
    generated_files = []

    print("🎨 开始生成政府报告专业图表...")
    print("=" * 50)

    try:
        generate_chart_01_visitor_trend()
        generated_files.append("chart_01_年度游客数量_柱状图.png")

        generate_chart_02_visitor_source()
        generated_files.append("chart_02_游客来源地分布_饼图.png")

        generate_chart_03_resident_occupation()
        generated_files.append("chart_03_居民职业分布_饼图.png")

        generate_chart_04_satisfaction_consumption()
        generated_files.append("chart_04_满意度与消费关系_散点图.png")

        generate_chart_05_digital_literacy()
        generated_files.append("chart_05_数字素养水平_条形图.png")

        generate_chart_06_policy_environment()
        generated_files.append("chart_06_政策环境匹配度_雷达图.png")

        generate_chart_07_investment_return()
        generated_files.append("chart_07_投资收益预测_柱状图.png")

        generate_chart_08_customer_preference()
        generated_files.append("chart_08_客户群体偏好_热力图.png")

        generate_chart_09_agriculture_structure()
        generated_files.append("chart_09_农业产业结构_饼图.png")

        generate_chart_10_monthly_visitors()
        generated_files.append("chart_10_月度客流量_折线图.png")

        generate_chart_11_project_timeline()
        generated_files.append("chart_11_项目建设进度_甘特图.png")

        generate_chart_12_risk_assessment()
        generated_files.append("chart_12_风险评估矩阵_散点图.png")

    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

    print("=" * 50)
    print(f"🎉 图表生成完成！共生成 {len(generated_files)} 个专业图表")
    return generated_files

if __name__ == "__main__":
    files = generate_all_charts()
    print("\n📊 生成的图表文件:")
    for i, file in enumerate(files, 1):
        print(f"{i}. {file}")

    print(f"\n📁 图表保存位置: {os.path.abspath(output_dir)}")
    print("🎯 图表类型统计:")
    print("   - 柱状图: 3个")
    print("   - 饼图: 4个")
    print("   - 散点图: 2个")
    print("   - 雷达图: 1个")
    print("   - 热力图: 1个")
    print("   - 折线图: 1个")
    print("   - 甘特图: 1个")
